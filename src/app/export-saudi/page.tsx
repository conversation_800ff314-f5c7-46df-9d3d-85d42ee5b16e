"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { DataTable } from "~/modules/saudi/components/datatable/data-table";
import AppHeader from "~/shared/components/AppHeader";
import React, { useEffect, useState } from "react";
import { SaudiDailyInformation } from "~/modules/saudi/models/SaudiDailyInformation.model";
import { api } from "~/trpc/react";
import { useSession } from "next-auth/react";

const columns: ColumnDef<SaudiDailyInformation>[] = [
  {
    accessorKey: "date",
    header: "DATE",
  },
  {
    accessorKey: "well",
    header: "WELL",
  },
  {
    accessorKey: "bitData",
    header: "BIT DATA",
  },
  {
    accessorKey: "bulk",
    header: "BULK",
  },
  {
    accessorKey: "drillString",
    header: "DRILL STRING",
  },
  {
    accessorKey: "drillStringTable",
    header: "DRILL STRING TABLE",
  },
  {
    accessorKey: "miscellaneous",
    header: "MISCELLANEOUS",
  },
  {
    accessorKey: "mudData",
    header: "MUD DATA",
  },
  {
    accessorKey: "mudTreatment",
    header: "MUD TREATMENT",
  },
  {
    accessorKey: "repair",
    header: "REPAIR",
  },
  {
    accessorKey: "truckBoats",
    header: "TRUCK BOATS",
  },
];

export default function ExportSaudiView() {
  const { data: session } = useSession();
  const { data: saudiData } =
    api.saudiDailyInformation.listSaudiDailyInformation.useQuery(
      { userId: session?.user.userId! },
      { enabled: !!session?.user.userId },
    );

  useEffect(() => {
    if (saudiData) {
      const information = saudiData.map((item) => {
        return new SaudiDailyInformation(item);
      });
      setSaudiDailyInformation(information);
    }
  }, [saudiData]);

  const [saudiDailyInformation, setSaudiDailyInformation] = useState<
    SaudiDailyInformation[]
  >([]);

  return (
    <>
      {/*{console.log(saudiDailyInformation)}*/}
      <AppHeader current={"export-saudi"}></AppHeader>
      <div className="mx-auto max-w-7xl">
        <div className="mt-8 mb-8">
          <h1 className="text-2xl font-semibold">Export Saudi Data</h1>
          <p className="mt-2 text-sm text-gray-500">
            Export all your data from Drillvisor for backup or analysis in other
            tools.
          </p>
        </div>
        <div className="bg-secondary my-10 shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="mt-6 space-y-4">
              <DataTable
                columns={columns}
                data={saudiDailyInformation}
              ></DataTable>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
