import { type SaudiBitDataResponse } from "~/modules/saudi/services/saudi-data";

export class SaudiBitData {
  constructor(
    public id: string,
    public dailyInformationId: string,
    public bitNumber: number | null,
    public mdIn: number | null,
    public mdOut: number | null,
    public runFootage: number | null,
    public hours: number | null,
    public averageROP: number | null,
    public wob: number | null,
    public rpm: number | null,
    public iadc: string | null,
    public size: number | null,
    public manufacturer: string | null,
    public pressure: number | null,
    public gpm: number | null,
    public jetVel: number | null,
    public dpAv: number | null,
    public dcAv: number | null,
    public bitHhp: number | null,
    public iRow: number | null,
    public oRow: number | null,
    public dc: string | null,
    public location: string | null,
    public bearings: string | null,
    public serialNumber: string | null,
    public type: string | null,
    public jets: string | null,
    public tfa: number | null,
    public gauge: number | null,
    public other: string | null,
    public poohReasons: string | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiBitDataResponse): SaudiBitData {
    return new SaudiBitData(
      response.id,
      response.dailyInformationId,
      response.bitNumber,
      response.mdIn,
      response.mdOut,
      response.runFootage,
      response.hours,
      response.averageROP,
      response.wob,
      response.rpm,
      response.iadc,
      response.size,
      response.manufacturer,
      response.pressure,
      response.gpm,
      response.jetVel,
      response.dpAv,
      response.dcAv,
      response.bitHhp,
      response.iRow,
      response.oRow,
      response.dc,
      response.location,
      response.bearings,
      response.serialNumber,
      response.type,
      response.jets,
      response.tfa,
      response.gauge,
      response.other,
      response.poohReasons,
      response.createdAt,
      response.updatedAt
    );
  }
}
