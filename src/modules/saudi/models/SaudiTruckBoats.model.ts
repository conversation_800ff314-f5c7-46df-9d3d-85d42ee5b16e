import { type SaudiTruckBoatsResponse } from "~/modules/saudi/services/saudi-data";

export class SaudiTruckBoats {
  constructor(
    public id: string,
    public dailyInformationId: string,
    public standbyTankers: string | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiTruckBoatsResponse): SaudiTruckBoats {
    return new SaudiTruckBoats(
      response.id,
      response.dailyInformationId,
      response.standbyTankers,
      response.createdAt,
      response.updatedAt
    );
  }
}
