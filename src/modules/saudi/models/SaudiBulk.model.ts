import { type SaudiBulkResponse } from "~/modules/saudi/services/saudi-data";

export class SaudiBulk {
  constructor(
    public id: string,
    public dailyInformationId: string,
    public drillWtrBbls: number | null,
    public potWtrBbls: number | null,
    public fuelBbls: number | null,
    public bariteSx: number | null,
    public bentonine: number | null,
    public cementGSx: number | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiBulkResponse): SaudiBulk {
    return new SaudiBulk(
      response.id,
      response.dailyInformationId,
      response.drillWtrBbls,
      response.potWtrBbls,
      response.fuelBbls,
      response.bariteSx,
      response.bentonine,
      response.cementGSx,
      response.createdAt,
      response.updatedAt
    );
  }
}
