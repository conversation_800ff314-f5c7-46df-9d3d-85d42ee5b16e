import { type SaudiDailyInformationResponse } from "~/modules/saudi/services/saudi-data";
import { SaudiWell } from "./SaudiWell.model";
import { SaudiBitData } from "./SaudiBitData.model";
import { SaudiMiscellaneus } from "./SaudiMiscellaneus.model";
import { SaudiMudData } from "./SaudiMudData.model";
import { SaudiRepair } from "./SaudiRepair.model";
import { SaudiTruckBoats } from "./SaudiTruckBoats.model";
import { SaudiBulk } from "./SaudiBulk.model";
import { SaudiDrillString } from "./SaudiDrillString.model";
import { SaudiPersonnel } from "./SaudiPersonnel.model";
import { SaudiProjectData } from "./SaudiProjectData.model";

// Main SaudiDailyInformation model class
export class SaudiDailyInformation {
  // Properties for the data table (based on the columns in the frontend)
  public date: string;
  public well: string;
  public bitData: string;
  public bulk: string;
  public drillingString: string;
  public drillingStringTable: string;
  public miscellaneous: string;
  public mudData: string;
  public repair: string;
  public mudTreatment: string;
  public truckBoats: string;

  // Full model properties
  public id: string;
  public wellId: string;
  public ddrId: string;
  public day: number | null;
  public rig: string | null;
  public ddrPage: number;
  public createdAt: Date;
  public updatedAt: Date;
  public wellModel: SaudiWell;
  public bitDataModel: SaudiBitData | null;
  public miscellaneusModel: SaudiMiscellaneus | null;
  public mudDataModel: SaudiMudData | null;
  public repairModel: SaudiRepair | null;
  public truckBoatsModel: SaudiTruckBoats | null;
  public bulkModel: SaudiBulk | null;
  public drillStringModel: SaudiDrillString | null;
  public personnelModel: SaudiPersonnel[];
  public projectDataModel: SaudiProjectData | null;

  constructor(saudiData: SaudiDailyInformationResponse) {
    this.mapToSaudiDailyInformation(saudiData);
  }

  mapToSaudiDailyInformation(saudiData: SaudiDailyInformationResponse): void {
    // Map main properties
    this.id = saudiData.id;
    this.wellId = saudiData.wellId;
    this.ddrId = saudiData.ddrId;
    this.day = saudiData.day;
    this.rig = saudiData.rig;
    this.ddrPage = saudiData.ddrPage;
    this.createdAt = saudiData.createdAt;
    this.updatedAt = saudiData.updatedAt;

    // Map related models
    this.wellModel = SaudiWell.fromResponse(saudiData.well);
    this.bitDataModel = saudiData.bitData ? SaudiBitData.fromResponse(saudiData.bitData) : null;
    this.miscellaneusModel = saudiData.miscellaneus ? SaudiMiscellaneus.fromResponse(saudiData.miscellaneus) : null;
    this.mudDataModel = saudiData.mudData ? SaudiMudData.fromResponse(saudiData.mudData) : null;
    this.repairModel = saudiData.repair ? SaudiRepair.fromResponse(saudiData.repair) : null;
    this.truckBoatsModel = saudiData.truckBoats ? SaudiTruckBoats.fromResponse(saudiData.truckBoats) : null;
    this.bulkModel = saudiData.bulk ? SaudiBulk.fromResponse(saudiData.bulk) : null;
    this.drillStringModel = saudiData.drillString ? SaudiDrillString.fromResponse(saudiData.drillString) : null;
    this.personnelModel = saudiData.personnel.map(p => SaudiPersonnel.fromResponse(p));
    this.projectDataModel = saudiData.projectData ? SaudiProjectData.fromResponse(saudiData.projectData) : null;

    // Map display properties for the data table
    this.date = saudiData.day?.toString() ?? "";
    this.well = saudiData.well.name ?? "";
    this.bitData = saudiData.bitData?.bitNumber?.toString() ?? "";
    this.bulk = saudiData.bulk?.id ?? "";
    this.drillingString = saudiData.drillString?.id ?? "";
    this.drillingStringTable = saudiData.drillString?.id ?? ""; // Note: drillStringTable is not in the response
    this.miscellaneous = saudiData.miscellaneus?.id ?? "";
    this.mudData = saudiData.mudData?.mudType ?? "";
    this.repair = saudiData.repair?.id ?? "";
    this.mudTreatment = saudiData.mudData?.mudType ?? ""; // Note: mudTreatment is not in the current response
    this.truckBoats = saudiData.truckBoats?.id ?? "";
  }
}