import { type SaudiDrillStringResponse } from "~/modules/saudi/services/saudi-data";

export class SaudiDrillString {
  constructor(
    public id: string,
    public dailyInformationId: string,
    public float: string | null,
    public bhaHours: number | null,
    public stringWt: number | null,
    public pickUp: number | null,
    public slackOff: number | null,
    public rotTorque: number | null,
    public jasrSerial: number | null,
    public jarsHours: number | null,
    public shockSubSerial: string | null,
    public shockSubHours: number | null,
    public mudMotorSerial: string | null,
    public mudMotorHours: number | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiDrillStringResponse): SaudiDrillString {
    return new SaudiDrillString(
      response.id,
      response.dailyInformationId,
      response.float,
      response.bhaHours,
      response.stringWt,
      response.pickUp,
      response.slackOff,
      response.rotTorque,
      response.jasrSerial,
      response.jarsHours,
      response.shockSubSerial,
      response.shockSubHours,
      response.mudMotorSerial,
      response.mudMotorHours,
      response.createdAt,
      response.updatedAt
    );
  }
}
