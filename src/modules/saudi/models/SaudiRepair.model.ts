import { type SaudiRepairResponse } from "~/modules/saudi/services/saudi-data";

export class SaudiRepair {
  constructor(
    public id: string,
    public dailyInformationId: string,
    public instrumentation: string | null,
    public other: string | null,
    public computerCommunicationVsatIssues: string | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiRepairResponse): SaudiRepair {
    return new SaudiRepair(
      response.id,
      response.dailyInformationId,
      response.instrumentation,
      response.other,
      response.computerCommunicationVsatIssues,
      response.createdAt,
      response.updatedAt
    );
  }
}
