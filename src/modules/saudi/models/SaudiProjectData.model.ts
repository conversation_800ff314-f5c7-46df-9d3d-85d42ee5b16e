import { type SaudiProjectDataResponse } from "~/modules/saudi/services/saudi-data";

export class SaudiProjectData {
  constructor(
    public id: string,
    public dailyInformationId: string,
    public charge: string | null,
    public wellbores: string | null,
    public thuraya: string | null,
    public rigFormatVsat: string | null,
    public last24HourOperation: string | null,
    public next24HourPlan: string | null,
    public location: string | null,
    public nextLocation: string | null,
    public currentDepth: number | null,
    public lastCsgSize: number | null,
    public measureDepth: number | null,
    public totalVerticalDepth: number | null,
    public linerSize: number | null,
    public tol: number | null,
    public prevDepth: number | null,
    public daysSinceSpud: number | null,
    public commDate: number | null,
    public circ: number | null,
    public footage: number | null,
    public distanceFromDHA: number | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiProjectDataResponse): SaudiProjectData {
    return new SaudiProjectData(
      response.id,
      response.dailyInformationId,
      response.charge,
      response.wellbores,
      response.thuraya,
      response.rigFormatVsat,
      response.last24HourOperation,
      response.next24HourPlan,
      response.location,
      response.nextLocation,
      response.currentDepth,
      response.lastCsgSize,
      response.measureDepth,
      response.totalVerticalDepth,
      response.linerSize,
      response.tol,
      response.prevDepth,
      response.daysSinceSpud,
      response.commDate,
      response.circ,
      response.footage,
      response.distanceFromDHA,
      response.createdAt,
      response.updatedAt
    );
  }
}
