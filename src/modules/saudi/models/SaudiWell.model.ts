import { type SaudiWellResponse } from "~/modules/saudi/services/saudi-data";

export class SaudiWell {
  constructor(
    public id: string,
    public name: string,
    public userId: string,
    public type: number | null,
    public latitude: number | null,
    public longitude: number | null,
    public startDate: Date | null,
    public country: string | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiWellResponse): SaudiWell {
    return new SaudiWell(
      response.id,
      response.name,
      response.userId,
      response.type,
      response.latitude?.toNumber() ?? null,
      response.longitude?.toNumber() ?? null,
      response.startDate,
      response.country,
      response.createdAt,
      response.updatedAt
    );
  }
}
