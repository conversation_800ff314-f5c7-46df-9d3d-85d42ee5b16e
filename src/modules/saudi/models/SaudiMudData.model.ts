import { type SaudiMudDataResponse } from "~/modules/saudi/services/saudi-data";

export class SaudiMudData {
  constructor(
    public id: string,
    public dailyInformationId: string,
    public weight: number | null,
    public flTemp: number | null,
    public funnel: number | null,
    public cakeHthp: number | null,
    public filtrateHthp: number | null,
    public cakeApi: number | null,
    public filtrateApi: number | null,
    public waterVol: number | null,
    public pv: number | null,
    public oilVol: number | null,
    public yp: number | null,
    public solidsVol: number | null,
    public elecStability: number | null,
    public sandVol: number | null,
    public rpm3: number | null,
    public rpm6: number | null,
    public lgs: number | null,
    public gelsSec: number | null,
    public gelsMin: number | null,
    public mbt: number | null,
    public ph: number | null,
    public mudType: string | null,
    public cappm: number | null,
    public clppm: number | null,
    public pptsSpurt: number | null,
    public pptTotal: number | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiMudDataResponse): SaudiMudData {
    return new SaudiMudData(
      response.id,
      response.dailyInformationId,
      response.weight,
      response.flTemp,
      response.funnel,
      response.cakeHthp,
      response.filtrateHthp,
      response.cakeApi,
      response.filtrateApi,
      response.waterVol,
      response.pv,
      response.oilVol,
      response.yp,
      response.solidsVol,
      response.elecStability,
      response.sandVol,
      response.rpm3,
      response.rpm6,
      response.lgs,
      response.gelsSec,
      response.gelsMin,
      response.mbt,
      response.ph,
      response.mudType,
      response.cappm,
      response.clppm,
      response.pptsSpurt,
      response.pptTotal,
      response.createdAt,
      response.updatedAt
    );
  }
}
