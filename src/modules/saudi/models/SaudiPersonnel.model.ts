import { type SaudiPersonnelResponse } from "~/modules/saudi/services/saudi-data";

export class SaudiPersonnel {
  constructor(
    public id: string,
    public dailyInformationId: string,
    public company: string | null,
    public category: string | null,
    public numberOfPersons: number | null,
    public onLocHours: number | null,
    public operatingHours: number | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiPersonnelResponse): SaudiPersonnel {
    return new SaudiPersonnel(
      response.id,
      response.dailyInformationId,
      response.company,
      response.category,
      response.numberOfPersons,
      response.onLocHours,
      response.operatingHours,
      response.createdAt,
      response.updatedAt
    );
  }
}
