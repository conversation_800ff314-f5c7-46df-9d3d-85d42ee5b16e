import { type SaudiMiscellaneusResponse } from "~/modules/saudi/services/saudi-data";

export class SaudiMiscellaneus {
  constructor(
    public id: string,
    public dailyInformationId: string,
    public bopTest: number | null,
    public bopDrills: number | null,
    public wind: string | null,
    public sea: string | null,
    public wheater: string | null,
    public dslta: number | null,
    public safetyMeeting: string | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiMiscellaneusResponse): SaudiMiscellaneus {
    return new SaudiMiscellaneus(
      response.id,
      response.dailyInformationId,
      response.bopTest,
      response.bopDrills,
      response.wind,
      response.sea,
      response.wheater,
      response.dslta,
      response.safetyMeeting,
      response.createdAt,
      response.updatedAt
    );
  }
}
